#!/bin/bash

# VSCode 二开项目自动合并冲突脚本
# 功能：自动处理非团队成员的冲突（采用 incoming），保留团队成员的冲突供手动解决

set -e

# 团队成员列表（可以根据需要修改）
TEAM_MEMBERS=("zhangheng05" "huyansheng" "houailing" "liyawei03")

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否在 git 仓库中
check_git_repo() {
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        log_error "当前目录不是 git 仓库"
        exit 1
    fi
}

# 检查是否有合并冲突
check_merge_conflicts() {
    if ! git diff --name-only --diff-filter=U | grep -q .; then
        log_info "没有发现合并冲突"
        return 1
    fi
    return 0
}

# 检查作者是否为团队成员
is_team_member() {
    local author="$1"
    for member in "${TEAM_MEMBERS[@]}"; do
        if [[ "$author" == *"$member"* ]]; then
            return 0
        fi
    done
    return 1
}

# 获取文件的冲突块信息
get_conflict_authors() {
    local file="$1"
    local temp_file=$(mktemp)
    
    # 获取当前分支和合并分支的提交信息
    local current_branch=$(git rev-parse --abbrev-ref HEAD)
    local merge_head=$(git rev-parse MERGE_HEAD 2>/dev/null || echo "")
    
    if [[ -z "$merge_head" ]]; then
        log_warning "未检测到正在进行的合并操作"
        rm -f "$temp_file"
        return 1
    fi
    
    # 分析冲突文件的最近修改者
    git blame "$file" 2>/dev/null | grep -E "^[a-f0-9]+" | cut -d'(' -f2 | cut -d' ' -f1 | sort | uniq > "$temp_file"
    
    local has_team_changes=false
    while IFS= read -r author; do
        if is_team_member "$author"; then
            has_team_changes=true
            break
        fi
    done < "$temp_file"
    
    rm -f "$temp_file"
    
    if [[ "$has_team_changes" == "true" ]]; then
        return 0  # 有团队成员的修改
    else
        return 1  # 没有团队成员的修改
    fi
}

# 自动解决冲突（采用 incoming 策略）
auto_resolve_conflict() {
    local file="$1"
    log_info "自动解决冲突文件: $file (采用 incoming 策略)"
    
    # 使用 git checkout 采用 incoming 版本
    if git checkout --theirs "$file" 2>/dev/null; then
        git add "$file"
        log_success "已自动解决: $file"
        return 0
    else
        log_error "无法自动解决: $file"
        return 1
    fi
}

# 主要处理函数
process_conflicts() {
    local conflicted_files=($(git diff --name-only --diff-filter=U))
    local auto_resolved=0
    local manual_required=()
    
    log_info "发现 ${#conflicted_files[@]} 个冲突文件"
    
    for file in "${conflicted_files[@]}"; do
        log_info "分析文件: $file"
        
        # 检查文件是否存在
        if [[ ! -f "$file" ]]; then
            log_warning "文件不存在，跳过: $file"
            continue
        fi
        
        # 检查是否有团队成员的修改
        if get_conflict_authors "$file"; then
            log_warning "检测到团队成员修改，需要手动解决: $file"
            manual_required+=("$file")
        else
            log_info "未检测到团队成员修改，尝试自动解决: $file"
            if auto_resolve_conflict "$file"; then
                ((auto_resolved++))
            else
                manual_required+=("$file")
            fi
        fi
    done
    
    # 输出处理结果
    echo
    log_success "处理完成！"
    log_info "自动解决的冲突: $auto_resolved 个"
    log_info "需要手动解决的冲突: ${#manual_required[@]} 个"
    
    if [[ ${#manual_required[@]} -gt 0 ]]; then
        echo
        log_warning "以下文件需要手动解决冲突："
        for file in "${manual_required[@]}"; do
            echo "  - $file"
        done
        echo
        log_info "请使用以下命令打开合并编辑器："
        log_info "code --merge <file>"
        echo
        log_info "或者使用 VSCode 的合并编辑器："
        log_info "git mergetool"
    else
        echo
        log_success "所有冲突已自动解决！"
        log_info "可以继续完成合并："
        log_info "git commit"
    fi
}

# 显示帮助信息
show_help() {
    echo "VSCode 二开项目自动合并冲突脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -t, --team     显示当前团队成员列表"
    echo "  -d, --dry-run  预览模式，不实际修改文件"
    echo
    echo "功能:"
    echo "  - 自动检测合并冲突"
    echo "  - 对于非团队成员的冲突，自动采用 incoming 策略"
    echo "  - 对于团队成员的冲突，保留供手动解决"
    echo
    echo "团队成员: ${TEAM_MEMBERS[*]}"
}

# 显示团队成员
show_team() {
    echo "当前团队成员列表:"
    for member in "${TEAM_MEMBERS[@]}"; do
        echo "  - $member"
    done
}

# 预览模式
dry_run_mode() {
    local conflicted_files=($(git diff --name-only --diff-filter=U))
    
    if [[ ${#conflicted_files[@]} -eq 0 ]]; then
        log_info "没有发现合并冲突"
        return 0
    fi
    
    log_info "预览模式 - 发现 ${#conflicted_files[@]} 个冲突文件"
    
    for file in "${conflicted_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            echo "  ❌ $file (文件不存在)"
            continue
        fi
        
        if get_conflict_authors "$file"; then
            echo "  🔧 $file (需要手动解决 - 团队成员修改)"
        else
            echo "  ✅ $file (可自动解决 - 采用 incoming)"
        fi
    done
}

# 主函数
main() {
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -t|--team)
                show_team
                exit 0
                ;;
            -d|--dry-run)
                check_git_repo
                if check_merge_conflicts; then
                    dry_run_mode
                fi
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查环境
    check_git_repo
    
    # 检查是否有冲突
    if ! check_merge_conflicts; then
        exit 0
    fi
    
    # 处理冲突
    process_conflicts
}

# 执行主函数
main "$@"
